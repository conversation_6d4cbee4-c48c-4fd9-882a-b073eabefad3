import React, { useState } from "react";
import { View, Text, StyleSheet, FlatList, TouchableOpacity, Dimensions } from "react-native";
import JobCard from "../../composants/JobCard";
import MapView, { Marker } from "react-native-maps";
import { useNavigation } from "@react-navigation/native";

interface Place {
  id: number;
  latitude: number;
  longitude: number;
  price: number;
  title: string;
  desc: string;
}

const places: Place[] = [
  { id: 1, latitude: 48.8566, longitude: 2.3522, price: 120, title: "Appartement cosy", desc: "2 pers · Paris centre" },
  { id: 2, latitude: 48.8666, longitude: 2.3622, price: 95, title: "Studio moderne", desc: "1 pers · Quartier animé" },
];

const jobs = [
  { id: "1", title: "Appartement cosy", dates: "Disponible toute la semaine", localisation: "Paris", price: 120, image: "https://picsum.photos/200", tag: "Appartement" },
  { id: "2", title: "Studio moderne", dates: "Disponible demain", localisation: "Paris", price: 95, image: "https://picsum.photos/201", tag: "Studio" },
];

export default function ExploreMainScreen() {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<"Liste" | "Carte">("Liste");

  return (
    <View style={styles.container}>
      {/* Tabs */}
      <View style={styles.tabsContainer}>
        {["Liste", "Carte"].map((tab) => (
          <TouchableOpacity
            key={tab}
            style={[styles.tabButton, activeTab === tab && styles.activeTab]}
            onPress={() => setActiveTab(tab as any)}
          >
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Contenu */}
      {activeTab === "Liste" ? (
        <FlatList
          data={jobs}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ padding: 20 }}
          renderItem={({ item }) => (
            <JobCard
              image={item.image}
              title={item.title}
              dates={item.dates}
              localisation={item.localisation}
              price={item.price}
              tag={item.tag}
            />
          )}
        />
      ) : (
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 48.8566,
            longitude: 2.3522,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {places.map((place) => (
            <Marker
              key={place.id}
              coordinate={{ latitude: place.latitude, longitude: place.longitude }}
              onPress={() => navigation.navigate("ExploreDetails", { place })}
            >
              <View style={styles.bubble}>
                <Text style={styles.price}>{place.price} €</Text>
              </View>
            </Marker>
          ))}
        </MapView>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  tabsContainer: { flexDirection: "row", justifyContent: "center", paddingVertical: 10, backgroundColor: "#fff" },
  tabButton: { marginHorizontal: 10, paddingVertical: 6, paddingHorizontal: 16, borderRadius: 20, backgroundColor: "#eee" },
  activeTab: { backgroundColor: "#000" },
  tabText: { color: "#555", fontWeight: "500" },
  activeTabText: { color: "#fff", fontWeight: "700" },
  map: { flex: 1 },
  bubble: { backgroundColor: "white", padding: 6, borderRadius: 20, borderWidth: 1, borderColor: "#ccc" },
  price: { fontWeight: "bold" },
});
