import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { createNativeStackNavigator } from "@react-navigation/native-stack";
import HomeStack from "./stacks/HomeStack";
import ExploreStack from "./stacks/ExploreStack";
import ProfileStack from "./stacks/ProfileStacks";
import ShiftStack from "./stacks/ShiftStack";
import MessagesScreen from "../screens/App/MessagesScreen";
import ExploreDetailsScreen from "../screens/App/Explore/ExploreDetailsScreen";

import { Colors } from "../constants/colors";
import { HouseIcon, MagnifyingGlassIcon, ChatTeardropIcon, UserCircleIcon } from "phosphor-react-native";
import ShiftHistorique from "../screens/App/Shifts/ShiftHistorique";

const Tab = createBottomTabNavigator();
const RootStack = createNativeStackNavigator();

function Tabs() {
  return (
    <Tab.Navigator
      initialRouteName="HomeTab"
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: Colors.primary,
      }}
    >
      <Tab.Screen
        name="HomeTab"
        component={HomeStack}
        options={{
          title: "Home",
          tabBarIcon: ({ color, size }) => <HouseIcon color={color} size={size} weight="bold" />,
        }}
      />
      <Tab.Screen
        name="ExploreTab"
        component={ExploreStack}
        options={{
          title: "Explore",
          tabBarIcon: ({ color, size }) => <MagnifyingGlassIcon color={color} size={size} weight="bold" />,
        }}
      />
      <Tab.Screen
        name="ShiftTab"
        component={ShiftStack}
        options={{ title: "Shifts" }}
      />
      <Tab.Screen
        name="MessagesTab"
        component={MessagesScreen}
        options={{
          title: "Messages",
          tabBarIcon: ({ color, size }) => <ChatTeardropIcon color={color} size={size} weight="bold" />,
        }}
      />
      <Tab.Screen
        name="SettingsTab"
        component={ProfileStack}
        options={{
          title: "Settings",
          tabBarIcon: ({ color, size }) => <UserCircleIcon color={color} size={size} weight="bold" />,
        }}
      />
    </Tab.Navigator>
  );
}

export default function AppRootStack() {
  return (
    <RootStack.Navigator screenOptions={{ headerShown: true }}>
      {/* Tabs comme écran principal */}
      <RootStack.Screen name="Tabs" component={Tabs} options={{ headerShown: false }} />

      {/* Écrans globaux accessibles depuis n’importe quelle tab */}
      <RootStack.Screen
        name="ExploreDetails"
        component={ExploreDetailsScreen}
        
        options={{ title: "Détails", headerBackTitle: "Retour" }}
      />
      <RootStack.Screen name="ShiftHistorique" component={ShiftHistorique} options={{ title: "Historique", headerBackTitle: "Retour" }}/>

      {/* Tu peux ajouter d’autres écrans globaux ici */}
    </RootStack.Navigator>
  );
}
